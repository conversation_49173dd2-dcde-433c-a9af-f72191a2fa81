'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import {
  XMarkIcon,
  UserIcon,
  PencilIcon,
  PhoneIcon,
  LinkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { useNotifications } from '@/components/providers/notification-provider'

interface TeamMember {
  id?: string
  name: string
  email?: string
  position: string
  bio?: string
  profileImageUrl?: string
  phone: string
  linkedin?: string
  isactive?: boolean
  resumeUrl?: string
  // Personal Information
  birthdate?: string
  gender?: string
  maritalstatus?: string
  socialsecurityno?: string
  // Employment Information
  hiredate?: string
  salary?: number
  payrollmethod?: string
  // Address Information
  address?: string
  city?: string
  state?: string
  zipcode?: string
  country?: string
  // Additional URLs
  empresumeurl?: string
  twitterurl?: string
  githuburl?: string
  // Other
  notes?: string
  displayorder?: number
  createdAt?: string
  updatedAt?: string
}

interface TeamMemberFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: TeamMember) => Promise<void>
  title: string
  initialData?: TeamMember
}

export function TeamMemberFormModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData
}: TeamMemberFormModalProps) {
  const { showSuccess, showError, showWarning } = useNotifications()
  const [position, setPosition] = useState({ x: 0, y: 0 })
  const [size, setSize] = useState({ width: 600, height: 600 })
  const [isReady, setIsReady] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [resizeStart, setResizeStart] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const elementRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const actualContentRef = useRef<HTMLDivElement>(null)
  const animationFrameRef = useRef<number | null>(null)

  // Form state
  const [formData, setFormData] = useState<TeamMember>({
    name: initialData?.name || '',
    email: initialData?.email || '',
    position: initialData?.position || '',
    bio: initialData?.bio || '',
    profileImageUrl: initialData?.profileImageUrl || '',
    phone: initialData?.phone || '',
    linkedin: initialData?.linkedin || '',
    // Personal Information
    birthdate: initialData?.birthdate || '',
    gender: initialData?.gender || '',
    maritalstatus: initialData?.maritalstatus || '',
    socialsecurityno: initialData?.socialsecurityno || '',
    // Employment Information
    hiredate: initialData?.hiredate || '',
    salary: initialData?.salary || 0,
    payrollmethod: initialData?.payrollmethod || '',
    // Address Information
    address: initialData?.address || '',
    city: initialData?.city || '',
    state: initialData?.state || '',
    zipcode: initialData?.zipcode || '',
    country: initialData?.country || '',
    // Additional URLs
    empresumeurl: initialData?.empresumeurl || initialData?.resumeUrl || '',
    twitterurl: initialData?.twitterurl || '',
    githuburl: initialData?.githuburl || '',
    // Other
    notes: initialData?.notes || '',
    displayorder: initialData?.displayorder || 0,
    isactive: initialData?.isactive !== undefined ? initialData.isactive : true,
  })

  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string | null>(initialData?.profileImageUrl || null)
  const [selectedResumeFile, setSelectedResumeFile] = useState<File | null>(null)
  const [resumePreviewUrl, setResumePreviewUrl] = useState<string | null>(() => {
    const existingResumeUrl = initialData?.empresumeurl || initialData?.resumeUrl
    if (existingResumeUrl) {
      // Ensure we have the full URL for preview
      const fullUrl = existingResumeUrl.startsWith('http') 
        ? existingResumeUrl 
        : `${window.location.origin}${existingResumeUrl}`
      return fullUrl
    }
    return null
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Modal positioning and auto-height calculation
  useEffect(() => {
    if (isOpen && typeof window !== 'undefined') {
      setIsReady(false) // Hide modal while calculating
      
      // Modal dimensions
      const modalWidth = 1200
      const initialHeight = 600
      
      // Calculate center position
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      
      // Center calculation with viewport bounds
      const left = Math.max(0, (viewportWidth - modalWidth) / 2)
      const top = Math.max(0, (viewportHeight - initialHeight) / 2)
      
      // Ensure modal stays within viewport
      const finalLeft = Math.min(left, viewportWidth - modalWidth)
      const finalTop = Math.min(top, viewportHeight - initialHeight)
      
      // Set initial size and position
      setSize({ width: modalWidth, height: initialHeight })
      setPosition({ x: finalLeft, y: finalTop })
      
      // Show modal and calculate auto-height
      setTimeout(() => {
        setIsReady(true)
        
        // Auto-height calculation after content renders
        setTimeout(() => {
          if (actualContentRef.current) {
            const contentHeight = actualContentRef.current.scrollHeight
            const headerHeight = 80
            const footerHeight = 60
            const padding = 64
            const totalHeight = contentHeight + headerHeight + footerHeight + padding
            const maxHeight = viewportHeight * 0.9
            const newHeight = Math.min(maxHeight, totalHeight)
            
            // Re-center with new height
            const newTop = Math.max(0, (viewportHeight - newHeight) / 2)
            const finalNewTop = Math.min(newTop, viewportHeight - newHeight)
            
            setSize(prev => ({ ...prev, height: newHeight }))
            setPosition(prev => ({ ...prev, y: finalNewTop }))
          }
        }, 50)
      }, 10)
    } else {
      setIsReady(false)
    }
  }, [isOpen])

  // Handle border resizing
  const handleBorderResizeMouseDown = (e: React.MouseEvent, direction: string) => {
    e.preventDefault()
    e.stopPropagation()
    setIsResizing(true)
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height
    })
    // Store resize direction
    e.currentTarget.setAttribute('data-resize-direction', direction)
  }

  // Handle dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    })
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (isDragging && elementRef.current) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y
      
      // Keep modal within viewport bounds
      const maxX = window.innerWidth - size.width
      const maxY = window.innerHeight - size.height
      
      const constrainedX = Math.max(0, Math.min(maxX, newX))
      const constrainedY = Math.max(0, Math.min(maxY, newY))
      
      // Immediate DOM update - no state updates during drag for maximum speed
      elementRef.current.style.left = `${constrainedX}px`
      elementRef.current.style.top = `${constrainedY}px`
      elementRef.current.style.transform = 'none'
      elementRef.current.style.transition = 'none'
    } else if (isResizing && elementRef.current) {
      const deltaX = e.clientX - resizeStart.x
      const deltaY = e.clientY - resizeStart.y
      
      // Get resize direction from the element that initiated the resize
      const resizeElement = document.querySelector('[data-resize-direction]')
      const direction = resizeElement?.getAttribute('data-resize-direction') || 'se'
      
      let newWidth = resizeStart.width
      let newHeight = resizeStart.height
      
      // Handle different resize directions
      if (direction.includes('e')) { // East (right)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width + deltaX))
      }
      if (direction.includes('w')) { // West (left)
        newWidth = Math.max(200, Math.min(window.innerWidth, resizeStart.width - deltaX))
      }
      if (direction.includes('s')) { // South (bottom)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height + deltaY))
      }
      if (direction.includes('n')) { // North (top)
        newHeight = Math.max(1, Math.min(window.innerHeight, resizeStart.height - deltaY))
      }
      
      // Immediate DOM update - no state updates during resize for maximum speed
      elementRef.current.style.width = `${newWidth}px`
      elementRef.current.style.height = `${newHeight}px`
      elementRef.current.style.transition = 'none'
    }
  }, [isDragging, isResizing, dragStart, resizeStart, size.width, size.height])

  const handleMouseUp = useCallback(() => {
    // Sync state with final DOM position for consistency
    if (elementRef.current && (isDragging || isResizing)) {
      const rect = elementRef.current.getBoundingClientRect()
      setPosition({ x: rect.left, y: rect.top })
      setSize({ width: rect.width, height: rect.height })
    }
    
    // Restore transitions when interaction ends
    if (elementRef.current) {
      elementRef.current.style.transition = 'box-shadow 0.2s ease'
    }
    
    // Clean up resize direction attribute
    const resizeElement = document.querySelector('[data-resize-direction]')
    if (resizeElement) {
      resizeElement.removeAttribute('data-resize-direction')
    }
    
    setIsDragging(false)
    setIsResizing(false)
  }, [isDragging, isResizing])

  // Event listeners for dragging and resizing
  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove, { passive: true })
      document.addEventListener('mouseup', handleMouseUp, { passive: true })
      document.addEventListener('mouseleave', handleMouseUp, { passive: true })
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.removeEventListener('mouseleave', handleMouseUp)
      }
    }
  }, [isDragging, isResizing, handleMouseMove, handleMouseUp])

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (elementRef.current && !elementRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  // Handle window resize
  useEffect(() => {
    const handleWindowResize = () => {
      if (elementRef.current) {
        const rect = elementRef.current.getBoundingClientRect()
        const maxX = window.innerWidth - size.width
        const maxY = window.innerHeight - size.height
        
        if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
          const newX = Math.max(0, Math.min(maxX, position.x))
          const newY = Math.max(0, Math.min(maxY, position.y))
          setPosition({ x: newX, y: newY })
        }
      }
    }

    window.addEventListener('resize', handleWindowResize)
    return () => window.removeEventListener('resize', handleWindowResize)
  }, [position, size])

  // Form reset when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      // Reset form data when modal closes
      setFormData({
        name: initialData?.name || '',
        email: initialData?.email || '',
        position: initialData?.position || '',
        bio: initialData?.bio || '',
        profileImageUrl: initialData?.profileImageUrl || '',
        phone: initialData?.phone || '',
        linkedin: initialData?.linkedin || '',
        // Personal Information
        birthdate: initialData?.birthdate || '',
        gender: initialData?.gender || '',
        maritalstatus: initialData?.maritalstatus || '',
        socialsecurityno: initialData?.socialsecurityno || '',
        // Employment Information
        hiredate: initialData?.hiredate || '',
        salary: initialData?.salary || 0,
        payrollmethod: initialData?.payrollmethod || '',
        // Address Information
        address: initialData?.address || '',
        city: initialData?.city || '',
        state: initialData?.state || '',
        zipcode: initialData?.zipcode || '',
        country: initialData?.country || '',
        // Additional URLs
        empresumeurl: initialData?.empresumeurl || initialData?.empresumeurl || '',
        twitterurl: initialData?.twitterurl || '',
        githuburl: initialData?.githuburl || '',
        // Other
        notes: initialData?.notes || '',
        displayorder: initialData?.displayorder || 0,
        isactive: initialData?.isactive !== undefined ? initialData.isactive : true,
      })
      setSelectedFile(null)
      setPreviewUrl(initialData?.profileImageUrl || null)
      setSelectedResumeFile(null)
      // Set resume preview URL from existing data
      const existingResumeUrl = initialData?.empresumeurl || initialData?.resumeUrl
      if (existingResumeUrl) {
        // Ensure we have the full URL for preview
        const fullUrl = existingResumeUrl.startsWith('http') 
          ? existingResumeUrl 
          : `${window.location.origin}${existingResumeUrl}`
        setResumePreviewUrl(fullUrl)
      } else {
        setResumePreviewUrl(null)
      }
    } else {
      // Set initial data when modal opens for editing
      if (initialData) {
        setFormData({
          name: initialData.name,
          email: initialData.email,
          position: initialData.position,
          bio: initialData.bio || '',
          profileImageUrl: initialData.profileImageUrl || '',
          phone: initialData.phone || '',
          linkedin: initialData.linkedin || '',
          // Personal Information
          birthdate: initialData.birthdate || '',
          gender: initialData.gender || '',
          maritalstatus: initialData.maritalstatus || '',
          socialsecurityno: initialData.socialsecurityno || '',
          // Employment Information
          hiredate: initialData.hiredate || '',
          salary: initialData.salary || 0,
          payrollmethod: initialData.payrollmethod || '',
          // Address Information
          address: initialData.address || '',
          city: initialData.city || '',
          state: initialData.state || '',
          zipcode: initialData.zipcode || '',
          country: initialData.country || '',
          // Additional URLs
          empresumeurl: initialData.empresumeurl || initialData.resumeUrl || '',
          twitterurl: initialData.twitterurl || '',
          githuburl: initialData.githuburl || '',
          // Other
          notes: initialData.notes || '',
          displayorder: initialData.displayorder || 0,
          isactive: initialData.isactive !== undefined ? initialData.isactive : true,
        })
        setPreviewUrl(initialData.profileImageUrl || null)
        setSelectedResumeFile(null)
        // Set resume preview URL from existing data
        const existingResumeUrl = initialData.empresumeurl || initialData.resumeUrl
        if (existingResumeUrl) {
          // Ensure we have the full URL for preview
          const fullUrl = existingResumeUrl.startsWith('http') 
            ? existingResumeUrl 
            : `${window.location.origin}${existingResumeUrl}`
          setResumePreviewUrl(fullUrl)
        } else {
          setResumePreviewUrl(null)
        }
      }
    }
  }, [isOpen, initialData])


  // Auto-generate email from name (only for new members)
  useEffect(() => {
    if (!formData.name || initialData) return

    const email = formData.name
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '.')
      .trim() + '@company.com'
    
    setFormData(prev => ({ ...prev, email }))
  }, [formData.name, initialData])

  // Cleanup animation frame on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])

  // Validation functions
  const validateField = (field: string, value: any): string => {
    switch (field) {
      // Basic Information
      case 'name':
        if (!value || value.trim() === '') {
          return 'Name is required'
        }
        if (value.trim().length < 2) {
          return 'Name must be at least 2 characters'
        }
        if (value.trim().length > 100) {
          return 'Name must be less than 100 characters'
        }
        break
      case 'email':
        if (value && value.trim() !== '') {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
          if (!emailRegex.test(value)) {
            return 'Please enter a valid email address'
          }
          if (value.length > 255) {
            return 'Email must be less than 255 characters'
          }
        }
        break
      case 'position':
        if (!value || value.trim() === '') {
          return 'Position is required'
        }
        if (value.trim().length > 100) {
          return 'Position must be less than 100 characters'
        }
        break
      case 'phone':
        if (!value || value.trim() === '') {
          return 'Phone number is required'
        }
        const phoneRegex = /^[\d\s\-\+\(\)]+$/
        if (!phoneRegex.test(value)) {
          return 'Please enter a valid phone number'
        }
        if (value.length > 20) {
          return 'Phone number must be less than 20 characters'
        }
        break
      case 'bio':
        if (value && value.length > 1000) {
          return 'Biography must be less than 1000 characters'
        }
        break
      
      // Personal Information
      case 'birthdate':
        if (value && value.trim() !== '') {
          const date = new Date(value)
          const today = new Date()
          if (date > today) {
            return 'Birth date cannot be in the future'
          }
          const age = today.getFullYear() - date.getFullYear()
          if (age > 100) {
            return 'Please enter a valid birth date'
          }
        }
        break
      case 'gender':
        if (value && !['Male', 'Female', 'Other', 'Prefer not to say'].includes(value)) {
          return 'Please select a valid gender'
        }
        break
      case 'maritalstatus':
        if (value && !['Single', 'Married', 'Divorced', 'Widowed'].includes(value)) {
          return 'Please select a valid marital status'
        }
        break
      case 'socialsecurityno':
        if (value && value.trim() !== '') {
          const ssnRegex = /^\d{3}-\d{2}-\d{4}$/
          if (!ssnRegex.test(value)) {
            return 'SSN must be in format XXX-XX-XXXX'
          }
        }
        break
      
      // Employment Information
      case 'hiredate':
        if (value && value.trim() !== '') {
          const date = new Date(value)
          const today = new Date()
          if (date > today) {
            return 'Hire date cannot be in the future'
          }
          const yearsAgo = today.getFullYear() - date.getFullYear()
          if (yearsAgo > 50) {
            return 'Please enter a valid hire date'
          }
        }
        break
      case 'salary':
        if (value && value !== '') {
          const salary = parseFloat(value)
          if (isNaN(salary) || salary < 0) {
            return 'Salary must be a positive number'
          }
          if (salary > 10000000) {
            return 'Salary must be less than $10,000,000'
          }
        }
        break
      case 'payrollmethod':
        if (value && !['Hourly', 'Monthly', 'Annual', 'Contract'].includes(value)) {
          return 'Please select a valid payroll method'
        }
        break
      
      // Address Information
      case 'address':
        if (value && value.length > 255) {
          return 'Address must be less than 255 characters'
        }
        break
      case 'city':
        if (value && value.length > 100) {
          return 'City must be less than 100 characters'
        }
        break
      case 'state':
        if (value && value.length > 100) {
          return 'State must be less than 100 characters'
        }
        break
      case 'zipcode':
        if (value && value.trim() !== '') {
          const zipRegex = /^\d{5}(-\d{4})?$/
          if (!zipRegex.test(value)) {
            return 'ZIP code must be in format 12345 or 12345-6789'
          }
        }
        break
      case 'country':
        if (value && value.length > 100) {
          return 'Country must be less than 100 characters'
        }
        break
      
      // Additional Information
      case 'linkedin':
        if (value && value.trim() !== '') {
          try {
            const url = new URL(value)
            if (!url.hostname.includes('linkedin.com')) {
              return 'Please enter a valid LinkedIn URL'
            }
          } catch {
            return 'Please enter a valid URL'
          }
        }
        break
      case 'twitterurl':
        if (value && value.trim() !== '') {
          try {
            const url = new URL(value)
            if (!url.hostname.includes('twitter.com') && !url.hostname.includes('x.com')) {
              return 'Please enter a valid Twitter/X URL'
            }
          } catch {
            return 'Please enter a valid URL'
          }
        }
        break
      case 'githuburl':
        if (value && value.trim() !== '') {
          try {
            const url = new URL(value)
            if (!url.hostname.includes('github.com')) {
              return 'Please enter a valid GitHub URL'
            }
          } catch {
            return 'Please enter a valid URL'
          }
        }
        break
      case 'notes':
        if (value && value.length > 2000) {
          return 'Notes must be less than 2000 characters'
        }
        break
      case 'displayorder':
        if (value && value !== '') {
          const order = parseInt(value)
          if (isNaN(order) || order < 0) {
            return 'Display order must be a positive number'
          }
          if (order > 9999) {
            return 'Display order must be less than 10000'
          }
        }
        break
    }
    return ''
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}
    
    // Basic Information - Required fields
    newErrors.name = validateField('name', formData.name)
    newErrors.position = validateField('position', formData.position)
    newErrors.phone = validateField('phone', formData.phone)
    
    // Basic Information - Optional fields
    newErrors.email = validateField('email', formData.email)
    newErrors.bio = validateField('bio', formData.bio)
    
    // Personal Information
    newErrors.birthdate = validateField('birthdate', formData.birthdate)
    newErrors.gender = validateField('gender', formData.gender)
    newErrors.maritalstatus = validateField('maritalstatus', formData.maritalstatus)
    newErrors.socialsecurityno = validateField('socialsecurityno', formData.socialsecurityno)
    
    // Employment Information
    newErrors.hiredate = validateField('hiredate', formData.hiredate)
    newErrors.salary = validateField('salary', formData.salary)
    newErrors.payrollmethod = validateField('payrollmethod', formData.payrollmethod)
    
    // Address Information
    newErrors.address = validateField('address', formData.address)
    newErrors.city = validateField('city', formData.city)
    newErrors.state = validateField('state', formData.state)
    newErrors.zipcode = validateField('zipcode', formData.zipcode)
    newErrors.country = validateField('country', formData.country)
    
    // Additional Information
    newErrors.linkedin = validateField('linkedin', formData.linkedin)
    newErrors.twitterurl = validateField('twitterurl', formData.twitterurl)
    newErrors.githuburl = validateField('githuburl', formData.githuburl)
    newErrors.notes = validateField('notes', formData.notes)
    newErrors.displayorder = validateField('displayorder', formData.displayorder)
    
    // Remove empty errors
    Object.keys(newErrors).forEach(key => {
      if (!newErrors[key]) delete newErrors[key]
    })
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // Form handling functions
  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const handleFileSelect = async (file: File) => {
    console.log('File selected:', file.name, file.type, file.size)
    
    // Delete the old file if it exists
    if (formData.profileImageUrl) {
      try {
        const response = await fetch('/api/upload/delete-file', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fileUrl: formData.profileImageUrl })
        })
        
        if (!response.ok) {
          console.warn('Failed to delete old file from server')
        }
      } catch (error) {
        console.warn('Error deleting old file:', error)
      }
    }
    
    setSelectedFile(file)
    
    // Show preview immediately
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)
    
    // Upload the file immediately
    console.log('Uploading file immediately...')
    const uploadedUrl = await handleFileUploadWithFile(file)
    if (uploadedUrl) {
      console.log('File uploaded, updating form data with URL:', uploadedUrl)
      setFormData(prev => ({
        ...prev,
        profileImageUrl: uploadedUrl
      }))
    }
  }

  const handleResumeFileSelect = async (file: File) => {
    console.log('Resume file selected:', file.name, file.type, file.size)
    
    // Delete the old file if it exists
    if (formData.empresumeurl) {
      try {
        const response = await fetch('/api/upload/delete-file', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fileUrl: formData.empresumeurl })
        })
        
        if (!response.ok) {
          console.warn('Failed to delete old file from server')
        }
      } catch (error) {
        console.warn('Error deleting old file:', error)
      }
    }
    
    setSelectedResumeFile(file)
    
    // Show preview immediately
    const reader = new FileReader()
    reader.onloadend = () => {
      setResumePreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)
    
    // Upload the file immediately
    console.log('Uploading resume file immediately...')
    const uploadedUrl = await handleResumeFileUpload(file)
    if (uploadedUrl) {
      console.log('Resume file uploaded, updating form data with URL:', uploadedUrl)
      setFormData(prev => ({
        ...prev,
        empresumeurl: uploadedUrl
      }))
    }
  }

  const handleResumeFileUpload = async (file: File): Promise<string | null> => {
    try {
      console.log('Starting resume file upload:', file.name, file.type, file.size)
      
      // Validate file type for documents - PDF only
      const allowedTypes = ['application/pdf']
      if (!allowedTypes.includes(file.type)) {
        showError('Invalid File Type', 'Only PDF files are allowed for resume upload.')
        return null
      }
      
      // Validate file size (10MB limit for documents)
      const maxSize = 10 * 1024 * 1024 // 10MB
      if (file.size > maxSize) {
        showError('File Too Large', 'Maximum file size is 10MB. Please choose a smaller file.')
        return null
      }
      
      // Create FormData for upload
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', 'document') // Indicate this is a document upload
      
      console.log('Uploading resume file to /api/upload/team-resume...')
      
      try {
        // Upload to the dedicated team-resume endpoint
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
        
        const response = await fetch('/api/upload/team-resume', {
          method: 'POST',
          body: formData,
          signal: controller.signal,
        })
        
        clearTimeout(timeoutId)
        
        if (!response.ok) {
          throw new Error(`Upload failed: ${response.status} ${response.statusText}`)
        }
        
        const result = await response.json()
        console.log('Upload response:', result)
        
        if (result.success && result.data?.url) {
          console.log('Resume file uploaded successfully:', result.data.url)
          showSuccess('Resume Uploaded', 'Your resume has been uploaded successfully!')
          return result.data.url
        } else {
          throw new Error(result.error || 'Upload failed')
        }
        
      } catch (fetchError) {
        console.error('Resume upload fetch error:', fetchError)
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          showError('Upload Timeout', 'Resume upload took too long. Please try again.')
        } else {
          const errorMessage = fetchError instanceof Error ? fetchError.message : 'Unknown error'
          showError('Upload Failed', `Failed to upload resume: ${errorMessage}`)
        }
        return null
      }
      
    } catch (error) {
      console.error('Resume upload error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      showError('Upload Error', `Failed to upload resume: ${errorMessage}`)
      return null
    }
  }

  const handleResumePreview = async () => {
    if (formData.empresumeurl) {
      const fullUrl = formData.empresumeurl.startsWith('http') 
        ? formData.empresumeurl 
        : `${window.location.origin}${formData.empresumeurl}`
      
      try {
        // First check if the file exists
        const response = await fetch(fullUrl, { method: 'HEAD' })
        
        if (response.ok) {
          // File exists, open it with custom HTML wrapper to handle iframe errors
          const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
              <title>Resume Preview</title>
              <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                  font-family: Arial, sans-serif;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  overflow: hidden;
                }
                iframe {
                  width: 100%;
                  height: 100vh;
                  border: none;
                  display: block;
                }
                .error-container {
                  display: none;
                  background: white;
                  padding: 40px;
                  border-radius: 16px;
                  box-shadow: 0 10px 40px rgba(0,0,0,0.2);
                  text-align: center;
                  max-width: 500px;
                  width: 100%;
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                }
                .error-icon {
                  font-size: 80px;
                  margin-bottom: 20px;
                  animation: bounce 2s infinite;
                }
                @keyframes bounce {
                  0%, 100% { transform: translateY(0); }
                  50% { transform: translateY(-10px); }
                }
                .error-title {
                  font-size: 32px;
                  color: #dc2626;
                  margin-bottom: 15px;
                  font-weight: bold;
                }
                .error-message {
                  color: #6b7280;
                  font-size: 18px;
                  line-height: 1.6;
                  margin-bottom: 20px;
                }
                .error-details {
                  background: #fef2f2;
                  border-left: 4px solid #dc2626;
                  padding: 15px;
                  border-radius: 8px;
                  text-align: left;
                  margin-top: 20px;
                }
                .error-details-title {
                  font-weight: bold;
                  color: #dc2626;
                  margin-bottom: 10px;
                  font-size: 14px;
                }
                .error-details-list {
                  list-style: none;
                  padding: 0;
                  font-size: 14px;
                  color: #6b7280;
                }
                .error-details-list li {
                  padding: 5px 0;
                  padding-left: 20px;
                  position: relative;
                }
                .error-details-list li:before {
                  content: "•";
                  position: absolute;
                  left: 0;
                  color: #dc2626;
                  font-weight: bold;
                }
                .close-button {
                  background: #3b82f6;
                  color: white;
                  border: none;
                  padding: 12px 30px;
                  border-radius: 8px;
                  font-size: 16px;
                  font-weight: 600;
                  cursor: pointer;
                  margin-top: 20px;
                  transition: background 0.3s;
                }
                .close-button:hover {
                  background: #2563eb;
                }
              </style>
            </head>
            <body>
              <iframe id="pdfFrame" src="${fullUrl}" onload="handleLoad()" onerror="showError()"></iframe>
              
              <div id="errorContainer" class="error-container">
                <div class="error-icon">📄</div>
                <div class="error-title">Resume Not Found</div>
                <div class="error-message">
                  The resume file cannot be displayed at this time.
                </div>
                <div class="error-details">
                  <div class="error-details-title">Possible reasons:</div>
                  <ul class="error-details-list">
                    <li>The file has been deleted from the server</li>
                    <li>The file has been moved to a different location</li>
                    <li>The file path is incorrect or has changed</li>
                    <li>Network connectivity issues</li>
                  </ul>
                </div>
                <button class="close-button" onclick="window.close()">Close Window</button>
              </div>
              
              <script>
                function showError() {
                  document.getElementById('pdfFrame').style.display = 'none';
                  document.getElementById('errorContainer').style.display = 'block';
                }
                
                function handleLoad() {
                  try {
                    const iframe = document.getElementById('pdfFrame');
                    if (!iframe.contentDocument || iframe.contentDocument.body.innerHTML.trim() === '') {
                      showError();
                    }
                  } catch (e) {
                    showError();
                  }
                }
                
                // Fallback check after 3 seconds
                setTimeout(() => {
                  try {
                    const iframe = document.getElementById('pdfFrame');
                    if (!iframe.contentDocument || iframe.contentDocument.body.innerHTML.trim() === '') {
                      showError();
                    }
                  } catch (e) {
                    showError();
                  }
                }, 3000);
              </script>
            </body>
            </html>
          `
          
          const previewWindow = window.open('', '_blank')
          if (previewWindow) {
            previewWindow.document.write(htmlContent)
            previewWindow.document.close()
            previewWindow.focus()
          }
        } else {
          // File doesn't exist, show custom error page AND notification
          showError('Resume Not Found', 'The resume file cannot be found in the original folder. It may have been deleted or moved.')
          
          // Also show custom error page
          const errorHtml = `
            <!DOCTYPE html>
            <html>
            <head>
              <title>Resume - Not Found</title>
              <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body { 
                  font-family: Arial, sans-serif;
                  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  min-height: 100vh;
                  padding: 20px;
                }
                .error-container {
                  background: white;
                  padding: 40px;
                  border-radius: 16px;
                  box-shadow: 0 10px 40px rgba(0,0,0,0.2);
                  text-align: center;
                  max-width: 500px;
                  width: 100%;
                }
                .error-icon {
                  font-size: 80px;
                  margin-bottom: 20px;
                  animation: bounce 2s infinite;
                }
                @keyframes bounce {
                  0%, 100% { transform: translateY(0); }
                  50% { transform: translateY(-10px); }
                }
                .error-title {
                  font-size: 32px;
                  color: #dc2626;
                  margin-bottom: 15px;
                  font-weight: bold;
                }
                .error-message {
                  color: #6b7280;
                  font-size: 18px;
                  line-height: 1.6;
                  margin-bottom: 20px;
                }
                .error-details {
                  background: #fef2f2;
                  border-left: 4px solid #dc2626;
                  padding: 15px;
                  border-radius: 8px;
                  text-align: left;
                  margin-top: 20px;
                }
                .error-details-title {
                  font-weight: bold;
                  color: #dc2626;
                  margin-bottom: 10px;
                  font-size: 14px;
                }
                .error-details-list {
                  list-style: none;
                  padding: 0;
                  font-size: 14px;
                  color: #6b7280;
                }
                .error-details-list li {
                  padding: 5px 0;
                  padding-left: 20px;
                  position: relative;
                }
                .error-details-list li:before {
                  content: "•";
                  position: absolute;
                  left: 0;
                  color: #dc2626;
                  font-weight: bold;
                }
                .close-button {
                  background: #3b82f6;
                  color: white;
                  border: none;
                  padding: 12px 30px;
                  border-radius: 8px;
                  font-size: 16px;
                  font-weight: 600;
                  cursor: pointer;
                  margin-top: 20px;
                  transition: background 0.3s;
                }
                .close-button:hover {
                  background: #2563eb;
                }
              </style>
            </head>
            <body>
              <div class="error-container">
                <div class="error-icon">📄</div>
                <div class="error-title">Resume Not Found</div>
                <div class="error-message">
                  The resume file cannot be displayed at this time.
                </div>
                <div class="error-details">
                  <div class="error-details-title">Possible reasons:</div>
                  <ul class="error-details-list">
                    <li>The file has been deleted from the server</li>
                    <li>The file has been moved to a different location</li>
                    <li>The file path is incorrect or has changed</li>
                    <li>Network connectivity issues</li>
                  </ul>
                </div>
                <button class="close-button" onclick="window.close()">Close Window</button>
              </div>
            </body>
            </html>
          `
          
          const previewWindow = window.open('', '_blank')
          if (previewWindow) {
            previewWindow.document.write(errorHtml)
            previewWindow.document.close()
            previewWindow.focus()
          }
        }
      } catch (error) {
        // Network error, show custom error page AND notification
        showError('Preview Error', 'Unable to load the resume file. Please check your connection and try again.')
        
        // Also show custom error page
        const errorHtml = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Resume - Error</title>
            <style>
              * { margin: 0; padding: 0; box-sizing: border-box; }
              body { 
                font-family: Arial, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                min-height: 100vh;
                padding: 20px;
              }
              .error-container {
                background: white;
                padding: 40px;
                border-radius: 16px;
                box-shadow: 0 10px 40px rgba(0,0,0,0.2);
                text-align: center;
                max-width: 500px;
                width: 100%;
              }
              .error-icon {
                font-size: 80px;
                margin-bottom: 20px;
              }
              .error-title {
                font-size: 32px;
                color: #f59e0b;
                margin-bottom: 15px;
                font-weight: bold;
              }
              .error-message {
                color: #6b7280;
                font-size: 18px;
                line-height: 1.6;
                margin-bottom: 20px;
              }
              .close-button {
                background: #3b82f6;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                margin-top: 20px;
                transition: background 0.3s;
              }
              .close-button:hover {
                background: #2563eb;
              }
            </style>
          </head>
          <body>
            <div class="error-container">
              <div class="error-icon">⚠️</div>
              <div class="error-title">Connection Error</div>
              <div class="error-message">
                Unable to load the resume file. Please check your internet connection and try again.
              </div>
              <button class="close-button" onclick="window.close()">Close Window</button>
            </div>
          </body>
          </html>
        `
        
        const previewWindow = window.open('', '_blank')
        if (previewWindow) {
          previewWindow.document.write(errorHtml)
          previewWindow.document.close()
          previewWindow.focus()
        }
      }
    }
  }

  const handleResumeRemove = async () => {
    // Delete the original file from server if it exists
    if (formData.empresumeurl) {
      try {
        const response = await fetch('/api/upload/delete-file', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fileUrl: formData.empresumeurl })
        })
        
        if (!response.ok) {
          console.warn('Failed to delete original file from server')
        }
      } catch (error) {
        console.warn('Error deleting original file:', error)
      }
    }
    
    setFormData(prev => ({
      ...prev,
      empresumeurl: ''
    }))
    setSelectedResumeFile(null)
    setResumePreviewUrl(null)
    showSuccess('Resume Removed', 'Resume has been removed successfully!')
  }

  const handleResumeChange = () => {
    document.getElementById('resumeUpload')?.click()
  }

  const handleResumeDownload = () => {
    if (formData.empresumeurl) {
      const fullUrl = formData.empresumeurl.startsWith('http') 
        ? formData.empresumeurl 
        : `${window.location.origin}${formData.empresumeurl}`
      const link = document.createElement('a')
      link.href = fullUrl
      link.download = formData.empresumeurl.split('/').pop() || 'resume.pdf'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  const handleProfilePreview = () => {
    if (formData.profileImageUrl) {
      const fullUrl = formData.profileImageUrl.startsWith('http') 
        ? formData.profileImageUrl 
        : `${window.location.origin}${formData.profileImageUrl}`
      window.open(fullUrl, '_blank')
    }
  }

  const handleProfileRemove = async () => {
    // Delete the original file from server if it exists
    if (formData.profileImageUrl) {
      try {
        const response = await fetch('/api/upload/delete-file', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ fileUrl: formData.profileImageUrl })
        })
        
        if (!response.ok) {
          console.warn('Failed to delete original file from server')
        }
      } catch (error) {
        console.warn('Error deleting original file:', error)
      }
    }
    
    setFormData(prev => ({
      ...prev,
      profileImageUrl: ''
    }))
    setSelectedFile(null)
    setPreviewUrl(null)
    showSuccess('Profile Image Removed', 'Profile image has been removed successfully!')
  }

  const handleProfileChange = () => {
    document.getElementById('profileImageUpload')?.click()
  }


  const handleFileUploadWithFile = async (file: File): Promise<string | null> => {
    try {
      console.log('Starting file upload:', file.name, file.type, file.size)
      
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!allowedTypes.includes(file.type)) {
        showError('Invalid File Type', 'Only JPEG, PNG, WebP, GIF, PDF, and DOC files are allowed.')
        return null
      }
      
      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024 // 5MB
      if (file.size > maxSize) {
        showError('File Too Large', 'Maximum file size is 5MB. Please choose a smaller image.')
        return null
      }
      
      // Create FormData for upload
      const formData = new FormData()
      formData.append('file', file)
      
      console.log('Uploading file to /api/upload/team-photo...')
      
      try {
        // Upload to the team-photo endpoint with timeout
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
        
        const response = await fetch('/api/upload/team-photo', {
          method: 'POST',
          body: formData,
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        
        console.log('Upload response status:', response.status)
        
        if (!response.ok) {
          let errorMessage = `Upload failed with status ${response.status}`
          try {
            const errorText = await response.text()
            console.error('Upload failed:', errorText)
            errorMessage = errorText || errorMessage
          } catch (e) {
            console.error('Could not parse error response')
          }
          throw new Error(errorMessage)
        }
        
        const data = await response.json()
        console.log('Upload response data:', data)
        
        if (data.success && data.data?.url) {
          console.log('Upload successful, URL:', data.data.url)
          showSuccess('Upload Successful', 'Profile image has been uploaded.')
          return data.data.url
        } else {
          const errorMsg = data.error || 'Upload failed - no URL returned'
          console.error('Upload failed:', errorMsg)
          throw new Error(errorMsg)
        }
      } catch (fetchError) {
        console.error('Fetch error:', fetchError)
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          const timeoutError = new Error('Upload timeout - file may be too large or network is slow')
          throw timeoutError
        }
        throw fetchError
      }
    } catch (error) {
      console.error('Upload error:', error)
      showError('Upload Failed', error instanceof Error ? error.message : 'An unknown error occurred while uploading the image.')
      return null
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form before submitting
    if (!validateForm()) {
      showWarning('Validation Error', 'Please fix the errors in the form before submitting.')
      return
    }
    
    setIsSubmitting(true)

    try {
      let finalProfileImageUrl = formData.profileImageUrl

      // Upload file if selected and not already uploaded
      if (selectedFile && !finalProfileImageUrl) {
        console.log('Uploading selected file:', selectedFile.name)
        const uploadedUrl = await handleFileUploadWithFile(selectedFile)
        if (uploadedUrl) {
          console.log('File uploaded successfully, URL:', uploadedUrl)
          finalProfileImageUrl = uploadedUrl
        } else {
          console.log('File upload failed - continuing without image')
          showError('Upload Failed', 'Failed to upload image. Please try again.')
          setIsSubmitting(false)
          return
        }
      } else if (finalProfileImageUrl) {
        console.log('File already uploaded, using existing URL:', finalProfileImageUrl)
      }

      const submitData: TeamMember = {
        name: formData.name,
        email: formData.email,
        position: formData.position,
        bio: formData.bio,
        profileImageUrl: finalProfileImageUrl,
        phone: formData.phone,
        linkedin: formData.linkedin,
        // Personal Information
        birthdate: formData.birthdate,
        gender: formData.gender,
        maritalstatus: formData.maritalstatus,
        socialsecurityno: formData.socialsecurityno,
        // Employment Information
        hiredate: formData.hiredate,
        salary: formData.salary,
        payrollmethod: formData.payrollmethod,
        // Address Information
        address: formData.address,
        city: formData.city,
        state: formData.state,
        zipcode: formData.zipcode,
        country: formData.country,
        // Additional URLs
        empresumeurl: formData.empresumeurl,
        twitterurl: formData.twitterurl,
        githuburl: formData.githuburl,
        // Other
        notes: formData.notes,
        displayorder: formData.displayorder,
        isactive: formData.isactive,
      }


      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Submit error:', error)
      showError('Save Failed', error instanceof Error ? error.message : 'Failed to save team member. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen || !isReady) return null

  return (
    <>
      <style jsx>{`
        @keyframes modalAppear {
          0% {
            opacity: 0;
            transform: scale(0.9) translateY(-20px);
          }
          100% {
            opacity: 1;
            transform: scale(1) translateY(0);
          }
        }
        
        .resume-drop-zone:hover .resume-action-buttons {
          opacity: 1 !important;
          transform: translateY(-50%) !important;
        }
        
        .resume-action-buttons button:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
        }
        
        .profile-drop-zone:hover .profile-action-buttons {
          opacity: 1 !important;
          transform: translateY(-50%) !important;
        }
        
        .profile-action-buttons button:hover {
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2) !important;
        }
      `}</style>
      
      {/* Modal Backdrop/Overlay */}
      <div
        className="modal-overlay"
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.1)',
          zIndex: 99998,
          backdropFilter: 'blur(2px)',
        }}
        onClick={onClose}
      />
      
      {/* Modal Window */}
      <div
        ref={elementRef}
        className="modal-container modal-container-motion"
        style={{
          position: 'fixed',
          zIndex: 99999,
          width: `${size.width}px !important`,
          height: `${size.height}px`,
          left: `${position.x}px`,
          top: `${position.y}px`,
          transition: isDragging || isResizing ? 'none' : 'box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1)',
          animation: 'modalAppear 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)',
          cursor: 'default',
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Blue Header */}
        <div
          className="modal-header-motion blue-gradient"
          onMouseDown={handleMouseDown}
        >
          {/* Icon Container */}
          <div className="modal-header-motion-icon">
            <PencilIcon className="h-6 w-6" />
          </div>

          {/* Text Container */}
          <div className="modal-header-motion-text">
            <h3 className="modal-header-motion-title">
              {title}
            </h3>
            <p className="modal-header-motion-subtitle">
              Team Member Management
            </p>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="modal-header-motion-close"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div
          ref={contentRef}
          className="modal-content sample-style"
        >
          <div
            ref={actualContentRef}
            className="modal-content-body sample-style"
            style={{
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
              padding: '10px',
            }}
          >
            <form onSubmit={handleSubmit} className="modal-form">
              {/* TOP ROW: Basic Information and Profile Image - Side by Side */}
              <div style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
                {/* Basic Information Section */}
                <div 
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '16px',
                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                    border: '1px solid #e2e8f0',
                    flex: '1',
                  }}
                >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    marginBottom: '8px',
                  }}>
                    <div style={{
                      padding: '8px',
                      backgroundColor: '#dbeafe',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <UserIcon style={{ width: '16px', height: '16px', color: '#3b82f6' }} />
                    </div>
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#1e293b',
                      margin: 0,
                    }}>
                      Basic Information
                    </h3>
                  </div>
                  
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                      {/* Name */}
                      <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        Name <span style={{ color: '#ef4444' }}>*</span>
                      </label>
                      <input
                        type="text"
                        required
                        maxLength={100}
                        value={formData.name || ''}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        placeholder="Enter team member name"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: errors.name ? '1px solid #ef4444' : '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                          backgroundColor: errors.name ? '#fef2f2' : 'white',
                        }}
                        onFocus={(e) => {
                          if (!errors.name) e.target.style.borderColor = '#3b82f6'
                        }}
                        onBlur={(e) => {
                          if (!errors.name) e.target.style.borderColor = '#d1d5db'
                        }}
                      />
                      {errors.name && (
                        <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                          {errors.name}
                        </p>
                      )}
                    </div>

                    {/* Position */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        Position <span style={{ color: '#ef4444' }}>*</span>
                      </label>
                      <input
                        type="text"
                        required
                        maxLength={100}
                        value={formData.position || ''}
                        onChange={(e) => handleInputChange('position', e.target.value)}
                        placeholder="e.g., Senior Developer"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: errors.position ? '1px solid #ef4444' : '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                          backgroundColor: errors.position ? '#fef2f2' : 'white',
                        }}
                        onFocus={(e) => {
                          if (!errors.position) e.target.style.borderColor = '#3b82f6'
                        }}
                        onBlur={(e) => {
                          if (!errors.position) e.target.style.borderColor = '#d1d5db'
                        }}
                      />
                      {errors.position && (
                        <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                          {errors.position}
                        </p>
                      )}
                    </div>

                      {/* Email */}
                      <div>
                        <label style={{
                          display: 'block',
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#374151',
                        marginBottom: '6px',
                        }}>
                          Email Address
                        </label>
                        <input
                          type="email"
                          value={formData.email || ''}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          placeholder="<EMAIL>"
                          style={{
                            width: '100%',
                            padding: '10px 12px',
                            border: errors.email ? '1px solid #ef4444' : '1px solid #d1d5db',
                            borderRadius: '6px',
                            fontSize: '14px',
                            transition: 'border-color 0.2s',
                            backgroundColor: errors.email ? '#fef2f2' : 'white',
                          }}
                          onFocus={(e) => {
                            if (!errors.email) e.target.style.borderColor = '#3b82f6'
                          }}
                          onBlur={(e) => {
                            if (!errors.email) e.target.style.borderColor = '#d1d5db'
                          }}
                        />
                        {errors.email && (
                          <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                            {errors.email}
                          </p>
                        )}
                      </div>

                      {/* Phone */}
                      <div>
                        <label style={{
                          display: 'block',
                          fontSize: '14px',
                          fontWeight: '500',
                          color: '#374151',
                        marginBottom: '6px',
                        }}>
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          value={formData.phone || ''}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          placeholder="+****************"
                          style={{
                            width: '100%',
                            padding: '10px 12px',
                            border: errors.phone ? '1px solid #ef4444' : '1px solid #d1d5db',
                            borderRadius: '6px',
                            fontSize: '14px',
                            transition: 'border-color 0.2s',
                            backgroundColor: errors.phone ? '#fef2f2' : 'white',
                          }}
                          onFocus={(e) => {
                            if (!errors.phone) e.target.style.borderColor = '#3b82f6'
                          }}
                          onBlur={(e) => {
                            if (!errors.phone) e.target.style.borderColor = '#d1d5db'
                          }}
                        />
                        {errors.phone && (
                          <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                            {errors.phone}
                          </p>
                        )}
                      </div>


                    {/* Bio */}
                    <div style={{ gridColumn: '1 / -1' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Biography
                    </label>
                    <textarea
                      value={formData.bio || ''}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Write a brief biography of the team member..."
                        rows={4.5}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.bio ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        resize: 'vertical',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.bio ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.bio) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.bio) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.bio && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.bio}
                      </p>
                    )}
                    </div>
                  </div>
                </div>

                  {/* Profile Image Section */}
                  <div 
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      padding: '16px',
                      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                      border: '1px solid #e2e8f0',
                      flex: '0.5',
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '12px',
                      minWidth: '300px',
                      maxWidth: '400px',
                    }}
                  >
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    marginBottom: '8px',
                  }}>
                    <div style={{
                      padding: '8px',
                      backgroundColor: '#fef3c7',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}>
                      <svg style={{ width: '16px', height: '16px', color: '#f59e0b' }} fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9M19 9H14V4H19V9Z"/>
                      </svg>
                    </div>
                    <h3 style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: '#1e293b',
                      margin: 0,
                    }}>
                      Profile Image
                    </h3>
                  </div>

                  {/* Drag and Drop Area */}
                  <div
                    id="profileDropZone"
                    className="profile-drop-zone"
                    style={{
                      border: formData.profileImageUrl ? '2px solid #10b981' : '2px dashed #d1d5db',
                      borderRadius: '12px',
                      padding: '16px',
                      textAlign: 'center',
                      backgroundColor: formData.profileImageUrl ? '#f0fdf4' : '#fafafa',
                      transition: 'all 0.3s ease',
                      cursor: 'pointer',
                      position: 'relative',
                      minHeight: '200px',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onDragOver={(e) => {
                      e.preventDefault()
                      e.currentTarget.style.borderColor = '#3b82f6'
                      e.currentTarget.style.backgroundColor = '#eff6ff'
                    }}
                    onDragLeave={(e) => {
                      e.currentTarget.style.borderColor = formData.profileImageUrl ? '#10b981' : '#d1d5db'
                      e.currentTarget.style.backgroundColor = formData.profileImageUrl ? '#f0fdf4' : '#fafafa'
                    }}
                    onDrop={(e) => {
                      e.preventDefault()
                      const files = e.dataTransfer.files
                      if (files.length > 0) {
                        const file = files[0]
                        if (file.type.startsWith('image/')) {
                          handleFileSelect(file)
                        }
                      }
                      e.currentTarget.style.borderColor = formData.profileImageUrl ? '#10b981' : '#d1d5db'
                      e.currentTarget.style.backgroundColor = formData.profileImageUrl ? '#f0fdf4' : '#fafafa'
                    }}
                    onClick={() => document.getElementById('profileImageUpload')?.click()}
                  >
                    {(formData.profileImageUrl || previewUrl) ? (
                      <div style={{ position: 'relative', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <div style={{ position: 'relative' }}>
                          <img
                            src={formData.profileImageUrl || previewUrl || ''}
                            alt="Profile Image Preview"
                            style={{
                              width: '100%',
                              height: '180px',
                              objectFit: 'cover',
                              borderRadius: '6px',
                              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
                            }}
                            onError={(e) => {
                              e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIxIDE5VjVIMTlWMTlIMjFNMjEgM0gxOUMxNy45IDMgMTcgMy45IDE3IDVWMTlDMTcgMjAuMSAxNy45IDIxIDE5IDIxSDIxQzIyLjEgMjEgMjMgMjAuMSAyMyAxOVY1QzIzIDMuOSAyMi4xIDMgMjEgM1pNMTMgMTlIMTdWMTVIMTlMMTcgMTJMMTUgMTVIMTNWMTlaIiBmaWxsPSIjOTQ5Q0E3Ii8+Cjwvc3ZnPgo='
                            }}
                          />

                          {/* Profile Image Action Buttons - Right Side */}
                          <div 
                            className="profile-action-buttons"
                            style={{
                              position: 'absolute',
                              top: '50%',
                              right: '8px',
                              transform: 'translateY(-50%)',
                              display: 'flex',
                              gap: '8px',
                              opacity: '0',
                              transition: 'all 0.3s ease',
                              zIndex: 10,
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'flex-end',
                              maxWidth: '120px',
                            }}
                          >
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleProfilePreview()
                              }}
                              disabled={!formData.profileImageUrl}
                              style={{
                                padding: '10px 16px',
                                backgroundColor: '#10b981',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: formData.profileImageUrl ? 'pointer' : 'not-allowed',
                                fontSize: '13px',
                                fontWeight: '600',
                                opacity: formData.profileImageUrl ? 1 : 0.5,
                                transition: 'all 0.2s ease',
                                boxShadow: '0 4px 8px rgba(16, 185, 129, 0.3)',
                                width: '120px',
                                height: '40px',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '6px',
                                backdropFilter: 'blur(10px)',
                              }}
                              onMouseEnter={(e) => {
                                if (formData.profileImageUrl) {
                                  e.currentTarget.style.transform = 'translateY(-2px)'
                                  e.currentTarget.style.boxShadow = '0 6px 12px rgba(16, 185, 129, 0.4)'
                                }
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'translateY(0)'
                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(16, 185, 129, 0.3)'
                              }}
                            >
                              <i className="fas fa-eye" style={{ fontSize: '14px' }}></i>
                              <span>Preview</span>
                            </button>
                            
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleProfileChange()
                              }}
                              style={{
                                padding: '10px 16px',
                                backgroundColor: '#f59e0b',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer',
                                fontSize: '13px',
                                fontWeight: '600',
                                transition: 'all 0.2s ease',
                                boxShadow: '0 4px 8px rgba(245, 158, 11, 0.3)',
                                width: '120px',
                                height: '40px',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '6px',
                                backdropFilter: 'blur(10px)',
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.transform = 'translateY(-2px)'
                                e.currentTarget.style.boxShadow = '0 6px 12px rgba(245, 158, 11, 0.4)'
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'translateY(0)'
                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(245, 158, 11, 0.3)'
                              }}
                            >
                              <i className="fas fa-edit" style={{ fontSize: '14px' }}></i>
                              <span>Change</span>
                            </button>
                            
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation()
                                handleProfileRemove()
                              }}
                              disabled={!formData.profileImageUrl}
                              style={{
                                padding: '10px 16px',
                                backgroundColor: '#ef4444',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: formData.profileImageUrl ? 'pointer' : 'not-allowed',
                                fontSize: '13px',
                                fontWeight: '600',
                                opacity: formData.profileImageUrl ? 1 : 0.5,
                                transition: 'all 0.2s ease',
                                boxShadow: '0 4px 8px rgba(239, 68, 68, 0.3)',
                                width: '120px',
                                height: '40px',
                                whiteSpace: 'nowrap',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                gap: '6px',
                                backdropFilter: 'blur(10px)',
                              }}
                              onMouseEnter={(e) => {
                                if (formData.profileImageUrl) {
                                  e.currentTarget.style.transform = 'translateY(-2px)'
                                  e.currentTarget.style.boxShadow = '0 6px 12px rgba(239, 68, 68, 0.4)'
                                }
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.transform = 'translateY(0)'
                                e.currentTarget.style.boxShadow = '0 4px 8px rgba(239, 68, 68, 0.3)'
                              }}
                            >
                              <i className="fas fa-trash" style={{ fontSize: '14px' }}></i>
                              <span>Remove</span>
                            </button>
                          </div>
                        </div>
                        
                        <div style={{
                          fontSize: '12px',
                          color: '#6b7280',
                          backgroundColor: '#f3f4f6',
                          padding: '8px 12px',
                          borderRadius: '6px',
                          wordBreak: 'break-all',
                          textAlign: 'left',
                          fontFamily: 'monospace',
                          lineHeight: '1.4',
                          width: '100%',
                          boxSizing: 'border-box',
                          marginTop: '8px',
                        }}>
                          {formData.profileImageUrl ? (
                            <span style={{ color: '#10b981', fontWeight: '500' }}>✓ Uploaded: {formData.profileImageUrl}</span>
                          ) : (
                            <span style={{ color: '#f59e0b' }}>⏳ Uploading image...</span>
                          )}
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div style={{
                          width: '60px',
                          height: '60px',
                          backgroundColor: '#f3f4f6',
                          borderRadius: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          margin: '0 auto 16px',
                        }}>
                          <UserIcon style={{ width: '28px', height: '28px', color: '#9ca3af' }} />
                        </div>
                        <p style={{
                          fontSize: '14px',
                          color: '#374151',
                          margin: '0 0 6px 0',
                          fontWeight: '500',
                        }}>
                          Drop your image here
                        </p>
                        <p style={{
                          fontSize: '12px',
                          color: '#6b7280',
                          margin: '0',
                          lineHeight: '1.4',
                        }}>
                          or click to browse files
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <input
                    id="profileImageUpload"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        handleFileSelect(file)
                      }
                    }}
                    style={{ display: 'none' }}
                  />
                  </div>
                </div>

              {/* MIDDLE ROW: Left Column (Personal, Employment, Address) + Right Column (Resume, Status) */}
              <div style={{ display: 'flex', gap: '12px', marginBottom: '12px' }}>
                {/* LEFT COLUMN: Personal, Employment, Address sections */}
                <div style={{ flex: '1', display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {/* Personal Information Section */}
                <div 
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '16px',
                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                    border: '1px solid #e2e8f0',
                  }}
                >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '8px',
                  paddingBottom: '8px',
                }}>
                  <div style={{
                    padding: '8px',
                    backgroundColor: '#dbeafe',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <UserIcon style={{ width: '16px', height: '16px', color: '#2563eb' }} />
                  </div>
                  <h3 style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1e293b',
                    margin: 0,
                  }}>
                    Personal Information
                  </h3>
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                  {/* Birth Date */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Birth Date
                    </label>
                    <input
                      type="date"
                      value={formData.birthdate || ''}
                      onChange={(e) => handleInputChange('birthdate', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.birthdate ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.birthdate ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.birthdate) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.birthdate) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.birthdate && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.birthdate}
                      </p>
                    )}
                  </div>

                  {/* Gender */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Gender
                    </label>
                    <select
                      value={formData.gender || ''}
                      onChange={(e) => handleInputChange('gender', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        backgroundColor: 'white',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    >
                      <option value="">Select gender</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                      <option value="Prefer not to say">Prefer not to say</option>
                    </select>
                  </div>

                  {/* Marital Status */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Marital Status
                    </label>
                    <select
                      value={formData.maritalstatus || ''}
                      onChange={(e) => handleInputChange('maritalstatus', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        backgroundColor: 'white',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    >
                      <option value="">Select status</option>
                      <option value="Single">Single</option>
                      <option value="Married">Married</option>
                      <option value="Divorced">Divorced</option>
                      <option value="Widowed">Widowed</option>
                    </select>
                  </div>

                  {/* Social Security Number */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Social Security Number
                    </label>
                    <input
                      type="text"
                      value={formData.socialsecurityno || ''}
                      onChange={(e) => handleInputChange('socialsecurityno', e.target.value)}
                      placeholder="XXX-XX-XXXX"
                      maxLength={11}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.socialsecurityno ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.socialsecurityno ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.socialsecurityno) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.socialsecurityno) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.socialsecurityno && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.socialsecurityno}
                      </p>
                    )}
                  </div>
                </div>
                </div>

                {/* Employment Information Section */}
                <div 
                  style={{
                    backgroundColor: 'white',
                    borderRadius: '12px',
                    padding: '16px',
                    boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                    border: '1px solid #e2e8f0',
                  }}
                >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '8px',
                  paddingBottom: '8px',
                }}>
                  <div style={{
                    padding: '8px',
                    backgroundColor: '#dcfce7',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <PencilIcon style={{ width: '16px', height: '16px', color: '#16a34a' }} />
                  </div>
                  <h3 style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1e293b',
                    margin: 0,
                  }}>
                    Employment Information
                  </h3>
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px' }}>
                  {/* Hire Date */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Hire Date
                    </label>
                    <input
                      type="date"
                      value={formData.hiredate || ''}
                      onChange={(e) => handleInputChange('hiredate', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    />
                  </div>

                  {/* Salary */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Salary
                    </label>
                    <input
                      type="number"
                      value={formData.salary || ''}
                      onChange={(e) => setFormData(prev => ({ ...prev, salary: parseFloat(e.target.value) || 0 }))}
                      placeholder="0.00"
                      step="0.01"
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.salary ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.salary ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.salary) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.salary) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.salary && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.salary}
                      </p>
                    )}
                  </div>

                  {/* Payroll Method */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Payroll Method
                    </label>
                    <select
                      value={formData.payrollmethod || ''}
                      onChange={(e) => handleInputChange('payrollmethod', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        backgroundColor: 'white',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    >
                      <option value="">Select method</option>
                      <option value="Hourly">Hourly</option>
                      <option value="Monthly">Monthly</option>
                      <option value="Annual">Annual</option>
                      <option value="Contract">Contract</option>
                    </select>
                </div>
                </div>
              </div>

              {/* Address Information Section */}
              <div 
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  padding: '16px',
                  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                  border: '1px solid #e2e8f0',
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '8px',
                  paddingBottom: '8px',
                }}>
                  <div style={{
                    padding: '8px',
                    backgroundColor: '#fce7f3',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <svg style={{ width: '16px', height: '16px', color: '#db2777' }} fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                  </div>
                  <h3 style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1e293b',
                    margin: 0,
                  }}>
                    Address Information
                  </h3>
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                  {/* Address */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Street Address
                    </label>
                    <input
                      type="text"
                      value={formData.address || ''}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                      placeholder="123 Main Street"
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    />
                  </div>

                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
                    {/* City */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        City
                      </label>
                      <input
                        type="text"
                        value={formData.city || ''}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="City name"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      />
                    </div>

                    {/* State */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        State/Province
                      </label>
                      <input
                        type="text"
                        value={formData.state || ''}
                        onChange={(e) => handleInputChange('state', e.target.value)}
                        placeholder="State or province"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      />
                    </div>

                    {/* Zip Code */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        Zip/Postal Code
                      </label>
                      <input
                        type="text"
                        value={formData.zipcode || ''}
                        onChange={(e) => handleInputChange('zipcode', e.target.value)}
                        placeholder="12345"
                        maxLength={10}
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: errors.zipcode ? '1px solid #ef4444' : '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                          backgroundColor: errors.zipcode ? '#fef2f2' : 'white',
                        }}
                        onFocus={(e) => {
                          if (!errors.zipcode) e.target.style.borderColor = '#3b82f6'
                        }}
                        onBlur={(e) => {
                          if (!errors.zipcode) e.target.style.borderColor = '#d1d5db'
                        }}
                      />
                      {errors.zipcode && (
                        <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                          {errors.zipcode}
                        </p>
                      )}
                    </div>

                    {/* Country */}
                    <div>
                      <label style={{
                        display: 'block',
                        fontSize: '14px',
                        fontWeight: '500',
                        color: '#374151',
                        marginBottom: '6px',
                      }}>
                        Country
                      </label>
                      <input
                        type="text"
                        value={formData.country || ''}
                        onChange={(e) => handleInputChange('country', e.target.value)}
                        placeholder="Country name"
                        style={{
                          width: '100%',
                          padding: '10px 12px',
                          border: '1px solid #d1d5db',
                          borderRadius: '6px',
                          fontSize: '14px',
                          transition: 'border-color 0.2s',
                        }}
                        onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                        onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                      />
                        </div>
                    </div>
                  </div>
                </div>
              </div>

                {/* RIGHT COLUMN: Resume and Status sections */}
                <div style={{ flex: '0.5', display: 'flex', flexDirection: 'column', gap: '12px' }}>
                  {/* Resume Section - Drag & Drop with Actions */}
                  <div 
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      padding: '16px',
                      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                      border: '1px solid #e2e8f0',
                      flex: '1',
                      minWidth: '300px',
                      maxWidth: '400px',
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      marginBottom: '8px',
                      paddingBottom: '8px',
                    }}>
                      <div style={{
                        padding: '8px',
                        backgroundColor: '#fef3c7',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <svg style={{ width: '16px', height: '16px', color: '#f59e0b' }} fill="currentColor" viewBox="0 0 24 24">
                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                      </div>
                      <h3 style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        color: '#1e293b',
                        margin: 0,
                      }}>
                        Resume Upload
                      </h3>
                    </div>
                    
                    {/* Resume Drag and Drop Area */}
                    <div
                      id="resumeDropZone"
                      className="resume-drop-zone"
                      style={{
                        border: 'none',
                        borderRadius: '0px',
                        padding: '0px',
                        textAlign: 'center',
                        backgroundColor: 'transparent',
                        transition: 'all 0.3s ease',
                        cursor: 'pointer',
                        minHeight: '200px',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                      onDragOver={(e) => {
                        e.preventDefault()
                        const files = e.dataTransfer.files
                        if (files.length > 0) {
                          const file = files[0]
                          if (file.type === 'application/pdf') {
                            e.currentTarget.style.borderColor = '#10b981'
                            e.currentTarget.style.backgroundColor = '#f0fdf4'
                          } else {
                            e.currentTarget.style.borderColor = '#ef4444'
                            e.currentTarget.style.backgroundColor = '#fef2f2'
                          }
                        } else {
                          e.currentTarget.style.borderColor = '#3b82f6'
                          e.currentTarget.style.backgroundColor = '#eff6ff'
                        }
                      }}
                      onDragLeave={(e) => {
                        e.preventDefault()
                        e.currentTarget.style.borderColor = 'transparent'
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }}
                      onDrop={(e) => {
                        e.preventDefault()
                        const files = e.dataTransfer.files
                        if (files.length > 0) {
                          const file = files[0]
                          if (file.type === 'application/pdf') {
                            handleResumeFileSelect(file)
                          } else {
                            showError('Invalid File Type', 'Please drop a PDF file. Only PDF resumes are accepted.')
                          }
                        }
                        e.currentTarget.style.borderColor = 'transparent'
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }}
                      onClick={() => document.getElementById('resumeUpload')?.click()}
                    >
                      {(formData.empresumeurl || resumePreviewUrl) ? (
                        <div style={{ position: 'relative', width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
                          {/* Resume Preview */}
                          <div 
                            className="resume-drop-zone"
                            style={{
                              width: '100%',
                              height: '350px',
                              backgroundColor: 'transparent',
                              border: 'none',
                              borderRadius: '0px',
                              marginBottom: '12px',
                              position: 'relative',
                              overflow: 'hidden',
                            }}
                          >
                            {(formData.empresumeurl || resumePreviewUrl) ? (
                              <div style={{
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                position: 'relative',
                              }}>
                                {/* PDF/DOC Preview */}
                                <div style={{
                                  flex: '1',
                                  display: 'flex',
                                  alignItems: 'stretch',
                                  justifyContent: 'stretch',
                                  backgroundColor: '#ffffff',
                                  border: 'none',
                                  borderRadius: '0px',
                                  margin: '0px',
                                  overflow: 'hidden',
                                }}>
                                  {(selectedResumeFile && selectedResumeFile.type === 'application/pdf') || (formData.empresumeurl && formData.empresumeurl.endsWith('.pdf')) ? (
                                    <iframe
                                      src={`${resumePreviewUrl || formData.empresumeurl || ''}#toolbar=0&navpanes=0&scrollbar=0&view=FitH`}
                                      style={{
                                        width: '100%',
                                        height: '100%',
                                        border: 'none',
                                        borderRadius: '4px',
                                      }}
                                      title="Resume Preview"
                                    />
                                  ) : (
                                    <div style={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      padding: '20px',
                                      textAlign: 'center',
                                    }}>
                                      <div style={{
                                        width: '60px',
                                        height: '60px',
                                        backgroundColor: '#10b981',
                                        borderRadius: '12px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        marginBottom: '12px',
                                      }}>
                                        <svg style={{ width: '24px', height: '24px', color: 'white' }} fill="currentColor" viewBox="0 0 24 24">
                                          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                        </svg>
                                      </div>
                                      <div style={{
                                        fontSize: '14px',
                                        color: '#374151',
                                        fontWeight: '500',
                                        marginBottom: '4px',
                                        wordBreak: 'break-all',
                                      }}>
                                        {selectedResumeFile ? selectedResumeFile.name : 'Resume File'}
                                      </div>
                                      <div style={{
                                        fontSize: '12px',
                                        color: '#6b7280',
                                        marginBottom: '8px',
                                      }}>
                                        {selectedResumeFile ? `${(selectedResumeFile.size / 1024 / 1024).toFixed(2)} MB` : 'Document uploaded'}
                                      </div>
                                      <div style={{
                                        fontSize: '11px',
                                        color: '#9ca3af',
                                        backgroundColor: '#f3f4f6',
                                        padding: '4px 8px',
                                        borderRadius: '4px',
                                      }}>
                                        PDF Document
                                      </div>
                                    </div>
                                  )}
                                </div>

                                {/* Action Buttons - Centered on File Area */}
                                <div 
                                  className="resume-action-buttons"
                                  style={{
                                    position: 'absolute',
                                    top: '50%',
                                    right: '12px',
                                    transform: 'translateY(-50%)',
                                    display: 'flex',
                                    gap: '8px',
                                    opacity: '0',
                                    transition: 'all 0.3s ease',
                                    zIndex: 10,
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                    alignItems: 'flex-end',
                                    maxWidth: '90%',
                                  }}
                                >
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleResumePreview()
                                    }}
                                    disabled={!formData.empresumeurl}
                                    style={{
                                      padding: '10px 16px',
                                      backgroundColor: '#10b981',
                                      color: 'white',
                                      border: 'none',
                                      borderRadius: '8px',
                                      cursor: formData.empresumeurl ? 'pointer' : 'not-allowed',
                                      fontSize: '13px',
                                      fontWeight: '600',
                                      opacity: formData.empresumeurl ? 1 : 0.5,
                                      transition: 'all 0.2s ease',
                                      boxShadow: '0 4px 8px rgba(16, 185, 129, 0.3)',
                                      width: '120px',
                                      height: '40px',
                                      whiteSpace: 'nowrap',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '6px',
                                      backdropFilter: 'blur(10px)',
                                    }}
                                    onMouseEnter={(e) => {
                                      if (formData.empresumeurl) {
                                        e.currentTarget.style.transform = 'translateY(-2px)'
                                        e.currentTarget.style.boxShadow = '0 6px 12px rgba(16, 185, 129, 0.4)'
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.transform = 'translateY(0)'
                                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(16, 185, 129, 0.3)'
                                    }}
                                  >
                                    <i className="fas fa-eye" style={{ fontSize: '14px' }}></i>
                                    <span>Preview</span>
                                  </button>
                                  
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleResumeDownload()
                                    }}
                                    disabled={!formData.empresumeurl}
                                    style={{
                                      padding: '10px 16px',
                                      backgroundColor: '#3b82f6',
                                      color: 'white',
                                      border: 'none',
                                      borderRadius: '8px',
                                      cursor: formData.empresumeurl ? 'pointer' : 'not-allowed',
                                      fontSize: '13px',
                                      fontWeight: '600',
                                      opacity: formData.empresumeurl ? 1 : 0.5,
                                      transition: 'all 0.2s ease',
                                      boxShadow: '0 4px 8px rgba(59, 130, 246, 0.3)',
                                      width: '120px',
                                      height: '40px',
                                      whiteSpace: 'nowrap',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '6px',
                                      backdropFilter: 'blur(10px)',
                                    }}
                                    onMouseEnter={(e) => {
                                      if (formData.empresumeurl) {
                                        e.currentTarget.style.transform = 'translateY(-2px)'
                                        e.currentTarget.style.boxShadow = '0 6px 12px rgba(59, 130, 246, 0.4)'
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.transform = 'translateY(0)'
                                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.3)'
                                    }}
                                  >
                                    <i className="fas fa-download" style={{ fontSize: '14px' }}></i>
                                    <span>Download</span>
                                  </button>
                                  
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleResumeChange()
                                    }}
                                    style={{
                                      padding: '10px 16px',
                                      backgroundColor: '#f59e0b',
                                      color: 'white',
                                      border: 'none',
                                      borderRadius: '8px',
                                      cursor: 'pointer',
                                      fontSize: '13px',
                                      fontWeight: '600',
                                      transition: 'all 0.2s ease',
                                      boxShadow: '0 4px 8px rgba(245, 158, 11, 0.3)',
                                      width: '120px',
                                      height: '40px',
                                      whiteSpace: 'nowrap',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '6px',
                                      backdropFilter: 'blur(10px)',
                                    }}
                                    onMouseEnter={(e) => {
                                      e.currentTarget.style.transform = 'translateY(-2px)'
                                      e.currentTarget.style.boxShadow = '0 6px 12px rgba(245, 158, 11, 0.4)'
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.transform = 'translateY(0)'
                                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(245, 158, 11, 0.3)'
                                    }}
                                  >
                                    <i className="fas fa-edit" style={{ fontSize: '14px' }}></i>
                                    <span>Change</span>
                                  </button>
                                  
                                  <button
                                    type="button"
                                    onClick={(e) => {
                                      e.stopPropagation()
                                      handleResumeRemove()
                                    }}
                                    disabled={!formData.empresumeurl}
                                    style={{
                                      padding: '10px 16px',
                                      backgroundColor: '#ef4444',
                                      color: 'white',
                                      border: 'none',
                                      borderRadius: '8px',
                                      cursor: formData.empresumeurl ? 'pointer' : 'not-allowed',
                                      fontSize: '13px',
                                      fontWeight: '600',
                                      opacity: formData.empresumeurl ? 1 : 0.5,
                                      transition: 'all 0.2s ease',
                                      boxShadow: '0 4px 8px rgba(239, 68, 68, 0.3)',
                                      width: '120px',
                                      height: '40px',
                                      whiteSpace: 'nowrap',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      gap: '6px',
                                      backdropFilter: 'blur(10px)',
                                    }}
                                    onMouseEnter={(e) => {
                                      if (formData.empresumeurl) {
                                        e.currentTarget.style.transform = 'translateY(-2px)'
                                        e.currentTarget.style.boxShadow = '0 6px 12px rgba(239, 68, 68, 0.4)'
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      e.currentTarget.style.transform = 'translateY(0)'
                                      e.currentTarget.style.boxShadow = '0 4px 8px rgba(239, 68, 68, 0.3)'
                                    }}
                                  >
                                    <i className="fas fa-trash" style={{ fontSize: '14px' }}></i>
                                    <span>Remove</span>
                                  </button>
                                </div>
                                
                              </div>
                            ) : (
                              <div style={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                                height: '100%',
                                padding: '20px',
                                textAlign: 'center',
                              }}>
                                <div style={{
                                  width: '60px',
                                  height: '60px',
                                  backgroundColor: '#f3f4f6',
                                  borderRadius: '12px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  marginBottom: '12px',
                                }}>
                                  <svg style={{ width: '24px', height: '24px', color: '#9ca3af' }} fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                                  </svg>
                                </div>
                                <div style={{
                                  fontSize: '14px',
                                  color: '#374151',
                                  fontWeight: '500',
                                  marginBottom: '4px',
                                }}>
                                  No resume uploaded
                                </div>
                                <div style={{
                                  fontSize: '12px',
                                  color: '#6b7280',
                                }}>
                                  Drop your CV here or click to browse
                                </div>
                              </div>
                            )}
                          </div>
                          
                          {/* Status Message */}
                          <div style={{
                            fontSize: '12px',
                            color: '#6b7280',
                            backgroundColor: '#f3f4f6',
                            padding: '8px 12px',
                            borderRadius: '6px',
                            wordBreak: 'break-all',
                            textAlign: 'left',
                            fontFamily: 'monospace',
                            lineHeight: '1.4',
                            width: '100%',
                            boxSizing: 'border-box',
                            marginBottom: '0px',
                          }}>
                            {formData.empresumeurl ? (
                              <span style={{ color: '#10b981', fontWeight: '500' }}>✓ Uploaded: {formData.empresumeurl}</span>
                            ) : (
                              <span style={{ color: '#f59e0b' }}>⏳ Uploading resume...</span>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div style={{
                            width: '50px',
                            height: '50px',
                            backgroundColor: '#f3f4f6',
                            borderRadius: '12px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            margin: '0 auto 12px',
                          }}>
                            <svg style={{ width: '20px', height: '20px', color: '#9ca3af' }} fill="currentColor" viewBox="0 0 24 24">
                              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                          </div>
                          <p style={{
                            fontSize: '14px',
                            color: '#374151',
                            margin: '0 0 4px 0',
                            fontWeight: '500',
                          }}>
                            Drop your PDF resume here
                          </p>
                          <p style={{
                            fontSize: '12px',
                            color: '#6b7280',
                            margin: '0',
                            lineHeight: '1.4',
                          }}>
                            or click to browse PDF files
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <input
                      id="resumeUpload"
                      type="file"
                      accept=".pdf"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          if (file.type === 'application/pdf') {
                            handleResumeFileSelect(file)
                          } else {
                            showError('Invalid File Type', 'Please select a PDF file. Only PDF resumes are accepted.')
                            // Clear the input
                            e.target.value = ''
                          }
                        }
                      }}
                      style={{ display: 'none' }}
                    />
                  </div>

                  {/* Status Section - Short */}
                  <div 
                    style={{
                      backgroundColor: 'white',
                      borderRadius: '12px',
                      padding: '16px',
                      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                      border: '1px solid #e2e8f0',
                    }}
                  >
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      marginBottom: '8px',
                      paddingBottom: '8px',
                    }}>
                      <div style={{
                        padding: '8px',
                        backgroundColor: '#fed7aa',
                        borderRadius: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}>
                        <InformationCircleIcon style={{ width: '16px', height: '16px', color: '#ea580c' }} />
                      </div>
                      <h3 style={{
                        fontSize: '18px',
                        fontWeight: '600',
                        color: '#1e293b',
                        margin: 0,
                      }}>
                        Status Settings
                      </h3>
                    </div>
                    
                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>

                      {/* Display Order */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                          Display Order
                    </label>
                    <input
                          type="number"
                          value={formData.displayorder || 0}
                          onChange={(e) => setFormData(prev => ({ ...prev, displayorder: parseInt(e.target.value) || 0 }))}
                          min="0"
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.displayorder ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.displayorder ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.displayorder) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.displayorder) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.displayorder && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.displayorder}
                      </p>
                    )}
                  </div>

                      {/* Is Active Toggle */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Status
                    </label>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                    }}>
                      <button
                        type="button"
                        onClick={() => handleInputChange('isactive', !formData.isactive)}
                        style={{
                          position: 'relative',
                          display: 'inline-flex',
                          height: '24px',
                          width: '48px',
                          alignItems: 'center',
                          borderRadius: '12px',
                          border: 'none',
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          backgroundColor: formData.isactive ? '#10b981' : '#d1d5db',
                          outline: 'none',
                        }}
                        onFocus={(e) => e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.5)'}
                        onBlur={(e) => e.target.style.boxShadow = 'none'}
                      >
                        <span style={{
                          position: 'absolute',
                          height: '20px',
                          width: '20px',
                          borderRadius: '50%',
                          backgroundColor: 'white',
                          transition: 'transform 0.2s',
                          transform: formData.isactive ? 'translateX(24px)' : 'translateX(2px)',
                          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                        }} />
                      </button>
                      <span style={{
                        fontSize: '14px',
                        fontWeight: '500',
                        color: formData.isactive ? '#10b981' : '#6b7280',
                      }}>
                        {formData.isactive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>

                    </div>
                  </div>
                </div>
              </div>

              {/* BOTTOM ROW: Additional Information - Full Width */}
              <div 
                style={{
                  backgroundColor: 'white',
                  borderRadius: '12px',
                  padding: '16px',
                  marginBottom: '6px',
                  boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
                  border: '1px solid #e2e8f0',
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '8px',
                  paddingBottom: '8px',
                }}>
                  <div style={{
                    padding: '8px',
                    backgroundColor: '#e0e7ff',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}>
                    <LinkIcon style={{ width: '16px', height: '16px', color: '#4f46e5' }} />
                  </div>
                  <h3 style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: '#1e293b',
                    margin: 0,
                  }}>
                    Additional Information
                  </h3>
                </div>
                
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '12px' }}>
                  {/* Twitter URL */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Twitter Profile URL
                    </label>
                    <input
                      type="url"
                      value={formData.twitterurl || ''}
                      onChange={(e) => handleInputChange('twitterurl', e.target.value)}
                      placeholder="https://twitter.com/username"
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    />
                  </div>

                  {/* GitHub URL */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      GitHub Profile URL
                    </label>
                    <input
                      type="url"
                      value={formData.githuburl || ''}
                      onChange={(e) => handleInputChange('githuburl', e.target.value)}
                      placeholder="https://github.com/username"
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                      }}
                      onFocus={(e) => e.target.style.borderColor = '#3b82f6'}
                      onBlur={(e) => e.target.style.borderColor = '#d1d5db'}
                    />
                  </div>

                  {/* LinkedIn URL */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      LinkedIn Profile URL
                    </label>
                        <input
                      type="url"
                      value={formData.linkedin || ''}
                      onChange={(e) => handleInputChange('linkedin', e.target.value)}
                      placeholder="https://linkedin.com/in/username"
                          style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.linkedin ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        backgroundColor: errors.linkedin ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.linkedin) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.linkedin) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.linkedin && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.linkedin}
                      </p>
                    )}
                  </div>

                  {/* Notes */}
                  <div style={{ gridColumn: '1 / -1' }}>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#374151',
                      marginBottom: '6px',
                    }}>
                      Additional Notes
                      </label>
                    <textarea
                      value={formData.notes || ''}
                      onChange={(e) => handleInputChange('notes', e.target.value)}
                      placeholder="Any additional notes or comments..."
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '10px 12px',
                        border: errors.notes ? '1px solid #ef4444' : '1px solid #d1d5db',
                        borderRadius: '6px',
                        fontSize: '14px',
                        transition: 'border-color 0.2s',
                        resize: 'vertical',
                        fontFamily: 'inherit',
                        backgroundColor: errors.notes ? '#fef2f2' : 'white',
                      }}
                      onFocus={(e) => {
                        if (!errors.notes) e.target.style.borderColor = '#3b82f6'
                      }}
                      onBlur={(e) => {
                        if (!errors.notes) e.target.style.borderColor = '#d1d5db'
                      }}
                    />
                    {errors.notes && (
                      <p style={{ fontSize: '12px', color: '#ef4444', margin: '4px 0 0 0', fontWeight: '500' }}>
                        {errors.notes}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>

        {/* Footer with Status and Action Buttons - Like Sample Modal */}
        <div style={{
          position: 'relative',
          backgroundColor: '#f8fafc',
          borderTop: '1px solid #e2e8f0',
          padding: '16px 24px',
          height: '70px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}>
          {/* Status Message - Left Aligned */}
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            color: '#64748b',
            fontSize: '14px',
            position: 'absolute',
            left: '24px',
            top: '50%',
            transform: 'translateY(-50%)',
          }}>
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M9 12l2 2 4-4"/>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
              <path d="M12 3c0 1-1 3-3 3s-3-2-3-3 1-3 3-3 3 2 3 3"/>
              <path d="M12 21c0-1 1-3 3-3s3 2 3 3-1 3-3 3-3-2-3-3"/>
            </svg>
            <span>All systems operational</span>
          </div>
          
          {/* Action Buttons - Auto-Centered */}
          <div style={{
            display: 'flex',
            gap: '12px',
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: 'translateX(-50%) translateY(-50%)',
          }}>
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              style={{
                padding: '12px 24px',
                backgroundColor: '#6b7280',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(107, 114, 128, 0.3)',
                opacity: isSubmitting ? 0.6 : 1,
              }}
            >
              Cancel
            </button>
            
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
              style={{
                padding: '12px 24px',
                backgroundColor: '#3b82f6',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                cursor: isSubmitting ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',
                opacity: isSubmitting ? 0.6 : 1,
              }}
            >
              {isSubmitting ? 'Saving...' : 'Save Team Member'}
            </button>
          </div>
        </div>

        {/* Border Resize Handles */}
        {/* Top border */}
        <div
          className="modal-resize-handle modal-resize-handle-top"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'n')}
        />
        
        {/* Right border */}
        <div
          className="modal-resize-handle modal-resize-handle-right"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'e')}
        />
        
        {/* Bottom border */}
        <div
          className="modal-resize-handle modal-resize-handle-bottom"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 's')}
        />
        
        {/* Left border */}
        <div
          className="modal-resize-handle modal-resize-handle-left"
          onMouseDown={(e) => handleBorderResizeMouseDown(e, 'w')}
        />
      </div>
    </>
  )
}